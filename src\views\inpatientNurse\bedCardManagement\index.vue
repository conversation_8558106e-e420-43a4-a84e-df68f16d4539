<script lang="ts" setup name="bedCardManagement">
import { reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Grid, View, Warning, Lock, UserFilled } from '@element-plus/icons-vue';

// 床位数据接口
interface BedInfo {
	id: string;
	bedNo: string;
	patientName?: string;
	patientAge?: number;
	patientGender?: string;
	diagnosis?: string;
	bedStatus: 'occupied' | 'available' | 'closed' | 'contaminated' | 'isolated';
	patientStatus?: 'critical' | 'serious' | 'normal';
	careLevel?: 'special' | 'level1' | 'level2' | 'level3';
}

// 响应式状态
const state = reactive({
	selectedDepartment: '全部科室',
	departments: ['全部科室', '内科', '外科', '康复科', '骨科'],
	statistics: {
		total: 120,
		occupied: 87,
		available: 25,
		criticalSerious: 8,
	},
	bedData: [] as BedInfo[],
});

// 初始化床位数据
const initBedData = () => {
	state.bedData = [
		{
			id: '01',
			bedNo: '01床',
			patientName: '张明',
			patientAge: 65,
			patientGender: '男',
			diagnosis: '急性心肌梗死',
			bedStatus: 'occupied',
			patientStatus: 'critical',
			careLevel: 'special',
		},
		{
			id: '02',
			bedNo: '02床',
			patientName: '李华',
			patientAge: 52,
			patientGender: '女',
			diagnosis: '糖尿病酮症酸中毒',
			bedStatus: 'occupied',
			patientStatus: 'serious',
			careLevel: 'level1',
		},
		{
			id: '03',
			bedNo: '03床',
			patientName: '王强',
			patientAge: 78,
			patientGender: '男',
			diagnosis: '肺结核',
			bedStatus: 'isolated',
			careLevel: 'level2',
		},
		{
			id: '04',
			bedNo: '04床',
			patientName: '赵丽',
			patientAge: 45,
			patientGender: '女',
			diagnosis: '类风湿关节炎',
			bedStatus: 'occupied',
			careLevel: 'level3',
		},
		{
			id: '05',
			bedNo: '05床',
			patientName: '张明',
			patientAge: 65,
			patientGender: '男',
			diagnosis: '高血压3级、冠心病',
			bedStatus: 'occupied',
			careLevel: 'level2',
		},
		{
			id: '06',
			bedNo: '06床',
			bedStatus: 'available',
		},
		{
			id: '07',
			bedNo: '07床',
			bedStatus: 'closed',
		},
		{
			id: '08',
			bedNo: '08床',
			bedStatus: 'contaminated',
		},
		{
			id: '09',
			bedNo: '09床',
			patientName: '陈杰',
			patientAge: 58,
			patientGender: '男',
			diagnosis: '慢性支气管炎急性发作',
			bedStatus: 'occupied',
			careLevel: 'level1',
		},
		{
			id: '10',
			bedNo: '10床',
			patientName: '刘芳',
			patientAge: 62,
			patientGender: '女',
			diagnosis: '骨质疏松症',
			bedStatus: 'occupied',
			careLevel: 'level3',
		},
		{
			id: '11',
			bedNo: '11床',
			patientName: '孙伟',
			patientAge: 49,
			patientGender: '男',
			diagnosis: '术后重症监护',
			bedStatus: 'occupied',
			patientStatus: 'serious',
			careLevel: 'special',
		},
		{
			id: '12',
			bedNo: '12床',
			patientName: '吴敏',
			patientAge: 72,
			patientGender: '女',
			diagnosis: '多器官功能衰竭',
			bedStatus: 'occupied',
			patientStatus: 'critical',
			careLevel: 'special',
		},
		{
			id: '13',
			bedNo: '13床',
			patientName: '周红',
			patientAge: 55,
			patientGender: '女',
			diagnosis: '慢性胃炎',
			bedStatus: 'occupied',
			careLevel: 'level2',
		},
		{
			id: '14',
			bedNo: '14床',
			bedStatus: 'isolated',
		},
		{
			id: '15',
			bedNo: '15床',
			bedStatus: 'available',
		},
		{
			id: '16',
			bedNo: '16床',
			bedStatus: 'contaminated',
		},
	];
};

// 科室筛选
const selectDepartment = (dept: string) => {
	state.selectedDepartment = dept;
	console.log(`筛选科室: ${dept}`);
};

// 查看床位详情
const viewBedDetails = (bed: BedInfo) => {
	console.log(`查看 ${bed.bedNo} 详情`);
	ElMessage.info(`查看 ${bed.bedNo} 详情`);
};

// 安排患者
const assignPatient = (bed: BedInfo) => {
	ElMessage.success(`为 ${bed.bedNo} 安排患者`);
};

// 清洁处理
const cleanBed = (bed: BedInfo) => {
	ElMessage.warning(`标记 ${bed.bedNo} 为需要清洁处理`);
};

// 隔离解除
const removeIsolation = (bed: BedInfo) => {
	ElMessage.info(`申请解除 ${bed.bedNo} 的隔离状态`);
};

// 获取床位状态文本
const getBedStatusText = (status: string) => {
	const statusMap: Record<string, string> = {
		occupied: '正常',
		available: '空床',
		closed: '关闭',
		contaminated: '污染',
		isolated: '隔离',
	};
	return statusMap[status] || '未知';
};

// 获取患者状态文本
const getPatientStatusText = (status?: string) => {
	const statusMap: Record<string, string> = {
		critical: '病危',
		serious: '病重',
	};
	return status ? statusMap[status] : '';
};

// 获取护理级别文本
const getCareLevelText = (level?: string) => {
	const levelMap: Record<string, string> = {
		special: '特级护理',
		level1: '一级护理',
		level2: '二级护理',
		level3: '三级护理',
	};
	return level ? levelMap[level] : '';
};

// 页面加载时初始化数据
onMounted(() => {
	initBedData();
});
</script>

<template>
	<div class="bed-card-management">
		<!-- 科室筛选和统计信息 -->
		<el-card shadow="hover" class="header-card">
			<div class="header-content">
				<div class="title-section">
					<h2 class="page-title">床位总览</h2>

					<div class="department-buttons">
						<el-button v-for="dept in state.departments" :key="dept" :type="state.selectedDepartment === dept ? 'primary' : 'default'" size="small" @click="selectDepartment(dept)" class="dept-btn">
							<el-icon v-if="dept === '全部科室'"><Grid /></el-icon>
							{{ dept }}
						</el-button>
					</div>
				</div>

				<!-- 床位统计 -->
				<el-row :gutter="16" class="statistics-row">
					<el-col :xs="12" :sm="6">
						<div class="stat-card stat-total">
							<p class="stat-label">总床位数</p>
							<p class="stat-value">{{ state.statistics.total }}</p>
						</div>
					</el-col>
					<el-col :xs="12" :sm="6">
						<div class="stat-card stat-occupied">
							<p class="stat-label">已占用</p>
							<p class="stat-value">{{ state.statistics.occupied }}</p>
						</div>
					</el-col>
					<el-col :xs="12" :sm="6">
						<div class="stat-card stat-available">
							<p class="stat-label">空床</p>
							<p class="stat-value">{{ state.statistics.available }}</p>
						</div>
					</el-col>
					<el-col :xs="12" :sm="6">
						<div class="stat-card stat-critical">
							<p class="stat-label">病危/病重</p>
							<p class="stat-value">{{ state.statistics.criticalSerious }}</p>
						</div>
					</el-col>
				</el-row>

				<!-- 状态图例 -->
				<div class="legend-section">
					<div class="legend-group">
						<span class="legend-title">床位状态：</span>
						<div class="legend-item">
							<span class="legend-dot bed-available"></span>
							<span>空床</span>
						</div>
						<div class="legend-item">
							<span class="legend-dot bed-closed"></span>
							<span>关闭</span>
						</div>
						<div class="legend-item">
							<span class="legend-dot bed-contaminated"></span>
							<span>污染</span>
						</div>
						<div class="legend-item">
							<span class="legend-dot bed-isolated"></span>
							<span>隔离</span>
						</div>
					</div>

					<div class="legend-group">
						<span class="legend-title">患者状态：</span>
						<div class="legend-item">
							<span class="legend-dot patient-critical"></span>
							<span>病危</span>
						</div>
						<div class="legend-item">
							<span class="legend-dot patient-serious"></span>
							<span>病重</span>
						</div>
					</div>

					<div class="legend-group">
						<span class="legend-title">护理级别：</span>
						<div class="legend-item">
							<span class="legend-dot care-special"></span>
							<span>特级护理</span>
						</div>
						<div class="legend-item">
							<span class="legend-dot care-level1"></span>
							<span>一级护理</span>
						</div>
						<div class="legend-item">
							<span class="legend-dot care-level2"></span>
							<span>二级护理</span>
						</div>
						<div class="legend-item">
							<span class="legend-dot care-level3"></span>
							<span>三级护理</span>
						</div>
					</div>
				</div>
			</div>
		</el-card>

		<!-- 床位网格 -->
		<el-card shadow="hover" class="bed-grid-card">
			<el-row :gutter="16" class="bed-grid">
				<el-col v-for="bed in state.bedData" :key="bed.id" :xs="12" :sm="8" :md="6" :lg="4" :xl="3" class="bed-col">
					<!-- 占用床位 -->
					<div v-if="bed.bedStatus === 'occupied'" :class="['bed-card', 'bed-occupied', `care-${bed.careLevel}`, { 'critical-pulse': bed.patientStatus === 'critical' }]">
						<div class="bed-header">
							<span class="bed-number">{{ bed.bedNo }}</span>
							<span class="bed-status-tag normal">
								{{ getBedStatusText(bed.bedStatus) }}
							</span>
						</div>

						<div class="bed-divider"></div>

						<div class="patient-info">
							<p class="patient-name">{{ bed.patientName }}</p>
							<p class="patient-details">{{ bed.patientAge }}岁 · {{ bed.patientGender }}</p>
							<p class="patient-diagnosis">{{ bed.diagnosis }}</p>
						</div>

						<div class="status-tags">
							<span v-if="bed.patientStatus" :class="['status-tag', `patient-${bed.patientStatus}`]">
								<el-icon><Warning /></el-icon>
								{{ getPatientStatusText(bed.patientStatus) }}
							</span>
							<span v-if="bed.careLevel" :class="['status-tag', `care-${bed.careLevel}-tag`]">
								{{ getCareLevelText(bed.careLevel) }}
							</span>
						</div>

						<div class="bed-actions">
							<el-button type="primary" text size="small" @click="viewBedDetails(bed)">
								<el-icon><View /></el-icon>
								详情
							</el-button>
						</div>
					</div>

					<!-- 空床 -->
					<div v-else-if="bed.bedStatus === 'available'" class="bed-card bed-empty">
						<div class="empty-bed-content">
							<div class="empty-bed-icon">
								<i class="fa fa-bed"></i>
							</div>
							<p class="bed-number">{{ bed.bedNo }}</p>
							<p class="bed-status">空床</p>
							<el-button type="primary" size="small" @click="assignPatient(bed)" class="assign-btn"> 安排患者 </el-button>
						</div>
					</div>

					<!-- 关闭床位 -->
					<div v-else-if="bed.bedStatus === 'closed'" class="bed-card bed-closed">
						<div class="closed-bed-content">
							<div class="closed-bed-icon">
								<el-icon><Lock /></el-icon>
							</div>
							<p class="bed-number">{{ bed.bedNo }}</p>
							<p class="bed-status">关闭</p>
							<el-button disabled size="small" class="maintenance-btn"> 维护中 </el-button>
						</div>
					</div>

					<!-- 污染床位 -->
					<div v-else-if="bed.bedStatus === 'contaminated'" class="bed-card bed-contaminated">
						<div class="contaminated-bed-content">
							<div class="contaminated-bed-icon">
								<i class="fa fa-biohazard"></i>
							</div>
							<p class="bed-number">{{ bed.bedNo }}</p>
							<p class="bed-status">污染</p>
							<el-button type="warning" size="small" @click="cleanBed(bed)" class="clean-btn"> 清洁处理 </el-button>
						</div>
					</div>

					<!-- 隔离床位 -->
					<div v-else-if="bed.bedStatus === 'isolated'" class="bed-card bed-isolated">
						<div class="isolated-bed-content">
							<div class="isolated-bed-icon">
								<el-icon><UserFilled /></el-icon>
							</div>
							<p class="bed-number">{{ bed.bedNo }}</p>
							<p class="bed-status">隔离</p>
							<el-button type="info" size="small" @click="removeIsolation(bed)" class="isolation-btn"> 隔离解除 </el-button>
						</div>
					</div>
				</el-col>
			</el-row>
		</el-card>
	</div>
</template>

<style lang="scss" scoped>
.bed-card-management {
	background-color: var(--next-bg-main-color);
	min-height: 100vh;
	padding: 16px;

	.header-card {
		margin-bottom: 16px;

		.header-content {
			.title-section {
				display: flex;
				flex-direction: column;
				gap: 16px;
				margin-bottom: 24px;

				@media (min-width: 768px) {
					flex-direction: row;
					justify-content: space-between;
					align-items: center;
				}

				.page-title {
					font-size: 20px;
					font-weight: bold;
					color: var(--el-text-color-primary);
					margin: 0;
				}

				.department-buttons {
					display: flex;
					flex-wrap: wrap;
					gap: 8px;

					.dept-btn {
						display: flex;
						align-items: center;
						gap: 4px;
					}
				}
			}

			.statistics-row {
				margin-bottom: 24px;

				.stat-card {
					border-radius: 8px;
					padding: 16px;
					text-align: center;

					.stat-label {
						font-size: 14px;
						margin: 0 0 8px 0;
					}

					.stat-value {
						font-size: 24px;
						font-weight: bold;
						margin: 0;
						color: var(--el-text-color-primary);
					}

					&.stat-total {
						background-color: #ecf5ff;
						border: 1px solid #d9ecff;

						.stat-label {
							color: #409eff;
						}
					}

					&.stat-occupied {
						background-color: #f0f9eb;
						border: 1px solid #e1f3d8;

						.stat-label {
							color: #67c23a;
						}
					}

					&.stat-available {
						background-color: #fdf6ec;
						border: 1px solid #faecd8;

						.stat-label {
							color: #e6a23c;
						}
					}

					&.stat-critical {
						background-color: #fef0f0;
						border: 1px solid #fde2e2;

						.stat-label {
							color: #f56c6c;
						}
					}
				}
			}

			.legend-section {
				display: flex;
				flex-wrap: wrap;
				gap: 24px;
				font-size: 14px;

				.legend-group {
					display: flex;
					flex-wrap: wrap;
					align-items: center;
					gap: 16px;

					.legend-title {
						font-weight: 500;
						color: var(--el-text-color-regular);
						white-space: nowrap;
					}

					.legend-item {
						display: flex;
						align-items: center;
						gap: 8px;

						.legend-dot {
							width: 12px;
							height: 12px;
							border-radius: 50%;
							display: inline-block;

							&.bed-available {
								background-color: #94a3b8;
							}

							&.bed-closed {
								background-color: #6b7280;
							}

							&.bed-contaminated {
								background-color: #92400e;
							}

							&.bed-isolated {
								background-color: #6366f1;
							}

							&.patient-critical {
								background-color: #7f1d1d;
								animation: pulse 2s infinite;
							}

							&.patient-serious {
								background-color: #dc2626;
							}

							&.care-special {
								background-color: #f97316;
							}

							&.care-level1 {
								background-color: #fbbf24;
							}

							&.care-level2 {
								background-color: #38bdf8;
							}

							&.care-level3 {
								background-color: #10b981;
							}
						}
					}
				}
			}
		}
	}

	.bed-grid-card {
		.bed-grid {
			.bed-col {
				margin-bottom: 16px;
			}

			.bed-card {
				background: white;
				border-radius: 8px;
				padding: 12px;
				box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
				transition: all 0.3s ease;
				height: 100%;
				min-height: 200px;

				&:hover {
					transform: translateY(-5px);
					box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
				}

				&.bed-occupied {
					border: 2px solid #10b981;

					&.care-special {
						border-color: #f97316;
					}

					&.care-level1 {
						border-color: #fbbf24;
					}

					&.care-level2 {
						border-color: #38bdf8;
					}

					&.care-level3 {
						border-color: #10b981;
					}

					&.critical-pulse {
						animation: pulse 2s infinite;
					}

					.bed-header {
						display: flex;
						justify-content: space-between;
						align-items: flex-start;
						margin-bottom: 8px;

						.bed-number {
							font-weight: bold;
							color: var(--el-text-color-primary);
						}

						.bed-status-tag {
							font-size: 12px;
							padding: 2px 8px;
							border-radius: 12px;

							&.normal {
								background-color: rgba(148, 163, 184, 0.1);
								color: #94a3b8;
							}
						}
					}

					.bed-divider {
						border-top: 1px solid #f1f2f3;
						margin: 8px 0;
					}

					.patient-info {
						margin-bottom: 12px;

						.patient-name {
							font-weight: 500;
							color: var(--el-text-color-primary);
							margin: 0 0 4px 0;
						}

						.patient-details {
							font-size: 12px;
							color: var(--el-text-color-secondary);
							margin: 0 0 4px 0;
						}

						.patient-diagnosis {
							font-size: 12px;
							color: var(--el-text-color-regular);
							margin: 0;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
						}
					}

					.status-tags {
						display: flex;
						flex-wrap: wrap;
						gap: 4px;
						margin-bottom: 12px;

						.status-tag {
							font-size: 12px;
							padding: 2px 8px;
							border-radius: 12px;
							display: flex;
							align-items: center;
							gap: 4px;

							&.patient-critical {
								background-color: #7f1d1d;
								color: white;
							}

							&.patient-serious {
								background-color: #dc2626;
								color: white;
							}

							&.care-special-tag {
								background-color: rgba(249, 115, 22, 0.1);
								color: #f97316;
							}

							&.care-level1-tag {
								background-color: rgba(251, 191, 36, 0.1);
								color: #fbbf24;
							}

							&.care-level2-tag {
								background-color: rgba(56, 189, 248, 0.1);
								color: #38bdf8;
							}

							&.care-level3-tag {
								background-color: rgba(16, 185, 129, 0.1);
								color: #10b981;
							}
						}
					}

					.bed-actions {
						display: flex;
						justify-content: flex-end;
					}
				}

				&.bed-empty {
					border: 2px dashed #94a3b8;

					.empty-bed-content {
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;
						text-align: center;
						height: 100%;
						min-height: 160px;

						.empty-bed-icon {
							color: #94a3b8;
							font-size: 32px;
							margin-bottom: 8px;
						}

						.bed-number {
							font-weight: bold;
							color: var(--el-text-color-regular);
							margin: 0 0 4px 0;
						}

						.bed-status {
							font-size: 14px;
							color: #94a3b8;
							margin: 0 0 12px 0;
						}

						.assign-btn {
							width: 100%;
						}
					}
				}

				&.bed-closed {
					background-color: #f5f5f5;
					border: 2px solid #6b7280;
					opacity: 0.7;

					.closed-bed-content {
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;
						text-align: center;
						height: 100%;
						min-height: 160px;

						.closed-bed-icon {
							color: #6b7280;
							font-size: 32px;
							margin-bottom: 8px;
						}

						.bed-number {
							font-weight: bold;
							color: var(--el-text-color-regular);
							margin: 0 0 4px 0;
						}

						.bed-status {
							font-size: 14px;
							color: #6b7280;
							margin: 0 0 12px 0;
						}

						.maintenance-btn {
							width: 100%;
							background-color: #d1d5db;
							color: #6b7280;
							cursor: not-allowed;
						}
					}
				}

				&.bed-contaminated {
					border: 2px solid #92400e;

					.contaminated-bed-content {
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;
						text-align: center;
						height: 100%;
						min-height: 160px;

						.contaminated-bed-icon {
							color: #92400e;
							font-size: 32px;
							margin-bottom: 8px;
						}

						.bed-number {
							font-weight: bold;
							color: var(--el-text-color-regular);
							margin: 0 0 4px 0;
						}

						.bed-status {
							font-size: 14px;
							color: #92400e;
							margin: 0 0 12px 0;
						}

						.clean-btn {
							width: 100%;
							background-color: #92400e;
							border-color: #92400e;

							&:hover {
								background-color: rgba(146, 64, 14, 0.8);
								border-color: rgba(146, 64, 14, 0.8);
							}
						}
					}
				}

				&.bed-isolated {
					border: 2px solid #6366f1;

					.isolated-bed-content {
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;
						text-align: center;
						height: 100%;
						min-height: 160px;

						.isolated-bed-icon {
							color: #6366f1;
							font-size: 32px;
							margin-bottom: 8px;
						}

						.bed-number {
							font-weight: bold;
							color: var(--el-text-color-regular);
							margin: 0 0 4px 0;
						}

						.bed-status {
							font-size: 14px;
							color: #6366f1;
							margin: 0 0 12px 0;
						}

						.isolation-btn {
							width: 100%;
							background-color: #6366f1;
							border-color: #6366f1;

							&:hover {
								background-color: rgba(99, 102, 241, 0.8);
								border-color: rgba(99, 102, 241, 0.8);
							}
						}
					}
				}
			}
		}
	}
}

// 脉冲动画
@keyframes pulse {
	0% {
		box-shadow: 0 0 0 0 rgba(127, 29, 29, 0.4);
	}
	70% {
		box-shadow: 0 0 0 10px rgba(127, 29, 29, 0);
	}
	100% {
		box-shadow: 0 0 0 0 rgba(127, 29, 29, 0);
	}
}

// 响应式调整
@media (max-width: 768px) {
	.bed-card-management {
		padding: 8px;

		.header-card {
			.header-content {
				.legend-section {
					.legend-group {
						width: 100%;
						margin-bottom: 8px;
					}
				}
			}
		}
	}
}
</style>
