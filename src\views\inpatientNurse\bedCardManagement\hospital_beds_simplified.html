<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>医院床位总览 - 简化版</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1e40af',
                        secondary: '#3b82f6',
                        // 患者病情状态
                        critical: '#7f1d1d',    // 病危 - 深暗红
                        serious: '#dc2626',     // 病重 - 鲜红色
                        // 护理级别
                        special: '#f97316',     // 特级护理 - 橙色
                        level1: '#fbbf24',      // 一级护理 - 明黄
                        level2: '#38bdf8',      // 二级护理 - 亮蓝
                        level3: '#10b981',      // 三级护理 - 绿色
                        // 床位状态
                        available: '#94a3b8',   // 空床 - 灰色
                        closed: '#6b7280',      // 关闭 - 深灰色
                        contaminated: '#92400e', // 污染 - 深棕色
                        isolated: '#6366f1',    // 隔离 - 靛蓝色
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .card-shadow {
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }
            .bed-hover {
                transition: all 0.3s ease;
            }
            .bed-hover:hover {
                transform: translateY(-5px);
                box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
            }
            .pulse-animation {
                animation: pulse 2s infinite;
            }
            @keyframes pulse {
                0% {
                    box-shadow: 0 0 0 0 rgba(127, 29, 29, 0.4);
                }
                70% {
                    box-shadow: 0 0 0 10px rgba(127, 29, 29, 0);
                }
                100% {
                    box-shadow: 0 0 0 0 rgba(127, 29, 29, 0);
                }
            }
            .content-auto {
                content-visibility: auto;
            }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">

    <!-- 科室筛选和统计信息 -->
    <div class="bg-white shadow-sm py-4">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
                <h2 class="text-xl font-bold text-gray-800 mb-3 md:mb-0">床位总览</h2>
                
                <div class="flex flex-wrap gap-2 w-full md:w-auto">
                    <button class="bg-primary text-white px-4 py-2 rounded-lg text-sm flex items-center">
                        <i class="fa fa-th-large mr-2"></i>全部科室
                    </button>
                    <button class="bg-white text-gray-700 border border-gray-300 px-4 py-2 rounded-lg text-sm hover:bg-gray-50 transition-colors">
                        内科
                    </button>
                    <button class="bg-white text-gray-700 border border-gray-300 px-4 py-2 rounded-lg text-sm hover:bg-gray-50 transition-colors">
                        外科
                    </button>
                    <button class="bg-white text-gray-700 border border-gray-300 px-4 py-2 rounded-lg text-sm hover:bg-gray-50 transition-colors">
                        康复科
                    </button>
                    <button class="bg-white text-gray-700 border border-gray-300 px-4 py-2 rounded-lg text-sm hover:bg-gray-50 transition-colors">
                        骨科
                    </button>
                </div>
            </div>
            
            <!-- 床位统计 -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <div class="bg-blue-50 border border-blue-100 rounded-lg p-3">
                    <p class="text-sm text-blue-600">总床位数</p>
                    <p class="text-2xl font-bold text-gray-800">120</p>
                </div>
                <div class="bg-green-50 border border-green-100 rounded-lg p-3">
                    <p class="text-sm text-green-600">已占用</p>
                    <p class="text-2xl font-bold text-gray-800">87</p>
                </div>
                <div class="bg-yellow-50 border border-yellow-100 rounded-lg p-3">
                    <p class="text-sm text-yellow-600">空床</p>
                    <p class="text-2xl font-bold text-gray-800">25</p>
                </div>
                <div class="bg-red-50 border border-red-100 rounded-lg p-3">
                    <p class="text-sm text-red-600">病危/病重</p>
                    <p class="text-2xl font-bold text-gray-800">8</p>
                </div>
            </div>
            
            <!-- 状态图例 -->
            <div class="flex flex-wrap items-center gap-x-4 gap-y-2 text-sm mb-2">
                <p class="text-gray-600 font-medium w-full sm:w-auto">床位状态：</p>
                <div class="flex items-center">
                    <span class="w-3 h-3 bg-available rounded-full mr-2"></span>
                    <span>空床</span>
                </div>
                <div class="flex items-center">
                    <span class="w-3 h-3 bg-closed rounded-full mr-2"></span>
                    <span>关闭</span>
                </div>
                <div class="flex items-center">
                    <span class="w-3 h-3 bg-contaminated rounded-full mr-2"></span>
                    <span>污染</span>
                </div>
                <div class="flex items-center">
                    <span class="w-3 h-3 bg-isolated rounded-full mr-2"></span>
                    <span>隔离</span>
                </div>
                
                <p class="text-gray-600 font-medium w-full sm:w-auto mt-2 sm:mt-0 sm:ml-6">患者状态：</p>
                <div class="flex items-center">
                    <span class="w-3 h-3 bg-critical rounded-full mr-2 pulse-animation"></span>
                    <span>病危</span>
                </div>
                <div class="flex items-center">
                    <span class="w-3 h-3 bg-serious rounded-full mr-2"></span>
                    <span>病重</span>
                </div>
                
                <p class="text-gray-600 font-medium w-full sm:w-auto mt-2 sm:mt-0 sm:ml-6">护理级别：</p>
                <div class="flex items-center">
                    <span class="w-3 h-3 bg-special rounded-full mr-2"></span>
                    <span>特级护理</span>
                </div>
                <div class="flex items-center">
                    <span class="w-3 h-3 bg-level1 rounded-full mr-2"></span>
                    <span>一级护理</span>
                </div>
                <div class="flex items-center">
                    <span class="w-3 h-3 bg-level2 rounded-full mr-2"></span>
                    <span>二级护理</span>
                </div>
                <div class="flex items-center">
                    <span class="w-3 h-3 bg-level3 rounded-full mr-2"></span>
                    <span>三级护理</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 床位网格 -->
    <div class="container mx-auto px-4 py-6">
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
            <!-- 1号床：病危 + 特级护理 + 正常床位状态 -->
            <div class="bg-white rounded-lg border-2 border-special p-3 card-shadow bed-hover pulse-animation">
                <div class="flex justify-between items-start mb-2">
                    <span class="font-bold text-gray-800">01床</span>
                    <span class="bg-available/10 text-available text-xs px-2 py-1 rounded-full">
                        正常
                    </span>
                </div>
                <div class="border-t border-gray-100 pt-2">
                    <p class="font-medium text-gray-800 mb-1">张明</p>
                    <p class="text-xs text-gray-500 mb-1">65岁 · 男</p>
                    <p class="text-xs text-gray-600 truncate">急性心肌梗死</p>
                </div>
                <div class="mt-2 flex flex-wrap gap-1">
                    <span class="bg-critical text-white text-xs px-2 py-0.5 rounded-full flex items-center">
                        <i class="fa fa-exclamation-triangle mr-1"></i>病危
                    </span>
                    <span class="bg-special/10 text-special text-xs px-2 py-0.5 rounded-full">特级护理</span>
                </div>
                <div class="mt-3 flex justify-end">
                    <button class="text-secondary text-sm hover:text-primary transition-colors">
                        <i class="fa fa-eye mr-1"></i>详情
                    </button>
                </div>
            </div>
            
            <!-- 2号床：病重 + 一级护理 + 正常床位状态 -->
            <div class="bg-white rounded-lg border-2 border-level1 p-3 card-shadow bed-hover">
                <div class="flex justify-between items-start mb-2">
                    <span class="font-bold text-gray-800">02床</span>
                    <span class="bg-available/10 text-available text-xs px-2 py-1 rounded-full">
                        正常
                    </span>
                </div>
                <div class="border-t border-gray-100 pt-2">
                    <p class="font-medium text-gray-800 mb-1">李华</p>
                    <p class="text-xs text-gray-500 mb-1">52岁 · 女</p>
                    <p class="text-xs text-gray-600 truncate">糖尿病酮症酸中毒</p>
                </div>
                <div class="mt-2 flex flex-wrap gap-1">
                    <span class="bg-serious text-white text-xs px-2 py-0.5 rounded-full flex items-center">
                        <i class="fa fa-warning mr-1"></i>病重
                    </span>
                    <span class="bg-level1/10 text-level1 text-xs px-2 py-0.5 rounded-full">一级护理</span>
                </div>
				<div class="mt-2 flex flex-wrap gap-1">
                    <span class="bg-serious text-white text-xs px-2 py-0.5 rounded-full flex items-center">
                        <i class="fa fa-warning mr-1"></i>病重
                    </span>
                    <span class="bg-level1/10 text-level1 text-xs px-2 py-0.5 rounded-full">一级护理</span>
                </div>
                <div class="mt-3 flex justify-end">
                    <button class="text-secondary text-sm hover:text-primary transition-colors">
                        <i class="fa fa-eye mr-1"></i>详情
                    </button>
                </div>
            </div>
            
            <!-- 3号床：二级护理 + 隔离床位 -->
            <div class="bg-white rounded-lg border-2 border-isolated p-3 card-shadow bed-hover">
                <div class="flex justify-between items-start mb-2">
                    <span class="font-bold text-gray-800">03床</span>
                    <span class="bg-isolated/10 text-isolated text-xs px-2 py-1 rounded-full flex items-center">
                        <i class="fa fa-ban mr-1"></i>隔离
                    </span>
                </div>
                <div class="border-t border-gray-100 pt-2">
                    <p class="font-medium text-gray-800 mb-1">王强</p>
                    <p class="text-xs text-gray-500 mb-1">78岁 · 男</p>
                    <p class="text-xs text-gray-600 truncate">肺结核</p>
                </div>
                <div class="mt-2 flex items-center">
                    <span class="bg-level2/10 text-level2 text-xs px-2 py-0.5 rounded-full">二级护理</span>
                </div>
                <div class="mt-3 flex justify-end">
                    <button class="text-secondary text-sm hover:text-primary transition-colors">
                        <i class="fa fa-eye mr-1"></i>详情
                    </button>
                </div>
            </div>
            
            <!-- 4号床：三级护理 + 正常床位 -->
            <div class="bg-white rounded-lg border-2 border-level3 p-3 card-shadow bed-hover">
                <div class="flex justify-between items-start mb-2">
                    <span class="font-bold text-gray-800">04床</span>
                    <span class="bg-available/10 text-available text-xs px-2 py-1 rounded-full">
                        正常
                    </span>
                </div>
                <div class="border-t border-gray-100 pt-2">
                    <p class="font-medium text-gray-800 mb-1">赵丽</p>
                    <p class="text-xs text-gray-500 mb-1">45岁 · 女</p>
                    <p class="text-xs text-gray-600 truncate">类风湿关节炎</p>
                </div>
                <div class="mt-2 flex items-center">
                    <span class="bg-level3/10 text-level3 text-xs px-2 py-0.5 rounded-full">三级护理</span>
                </div>
                <div class="mt-3 flex justify-end">
                    <button class="text-secondary text-sm hover:text-primary transition-colors">
                        <i class="fa fa-eye mr-1"></i>详情
                    </button>
                </div>
            </div>
            
            <!-- 5号床：二级护理 + 正常床位 -->
            <div class="bg-white rounded-lg border-2 border-level2 p-3 card-shadow bed-hover">
                <div class="flex justify-between items-start mb-2">
                    <span class="font-bold text-gray-800">05床</span>
                    <span class="bg-available/10 text-available text-xs px-2 py-1 rounded-full">
                        正常
                    </span>
                </div>
                <div class="border-t border-gray-100 pt-2">
                    <p class="font-medium text-gray-800 mb-1">张明</p>
                    <p class="text-xs text-gray-500 mb-1">65岁 · 男</p>
                    <p class="text-xs text-gray-600 truncate">高血压3级、冠心病</p>
                </div>
                <div class="mt-2 flex items-center">
                    <span class="bg-level2/10 text-level2 text-xs px-2 py-0.5 rounded-full">二级护理</span>
                </div>
                <div class="mt-3 flex justify-end">
                    <button class="text-secondary text-sm hover:text-primary transition-colors">
                        <i class="fa fa-eye mr-1"></i>详情
                    </button>
                </div>
            </div>
            
            <!-- 6号床：空床 -->
            <div class="bg-white rounded-lg border-2 border-dashed border-available p-3 flex flex-col items-center justify-center text-center h-full bed-hover">
                <div class="text-available mb-2">
                    <i class="fa fa-bed text-2xl"></i>
                </div>
                <p class="font-bold text-gray-600 mb-1">06床</p>
                <p class="text-sm text-available mb-3">空床</p>
                <button class="bg-secondary text-white text-sm px-3 py-1 rounded hover:bg-primary transition-colors w-full">
                    安排患者
                </button>
            </div>
            
            <!-- 7号床：关闭状态 -->
            <div class="bg-gray-100 rounded-lg border-2 border-closed p-3 flex flex-col items-center justify-center text-center h-full bed-hover opacity-70">
                <div class="text-closed mb-2">
                    <i class="fa fa-lock text-2xl"></i>
                </div>
                <p class="font-bold text-gray-600 mb-1">07床</p>
                <p class="text-sm text-closed mb-3">关闭</p>
                <button class="bg-gray-300 text-gray-700 text-sm px-3 py-1 rounded cursor-not-allowed w-full">
                    维护中
                </button>
            </div>
            
            <!-- 8号床：污染状态 -->
            <div class="bg-white rounded-lg border-2 border-contaminated p-3 flex flex-col items-center justify-center text-center h-full bed-hover">
                <div class="text-contaminated mb-2">
                    <i class="fa fa-biohazard text-2xl"></i>
                </div>
                <p class="font-bold text-gray-600 mb-1">08床</p>
                <p class="text-sm text-contaminated mb-3">污染</p>
                <button class="bg-contaminated text-white text-sm px-3 py-1 rounded hover:bg-contaminated/80 transition-colors w-full">
                    清洁处理
                </button>
            </div>
            
            <!-- 9号床：一级护理 + 正常床位 -->
            <div class="bg-white rounded-lg border-2 border-level1 p-3 card-shadow bed-hover">
                <div class="flex justify-between items-start mb-2">
                    <span class="font-bold text-gray-800">09床</span>
                    <span class="bg-available/10 text-available text-xs px-2 py-1 rounded-full">
                        正常
                    </span>
                </div>
                <div class="border-t border-gray-100 pt-2">
                    <p class="font-medium text-gray-800 mb-1">陈杰</p>
                    <p class="text-xs text-gray-500 mb-1">58岁 · 男</p>
                    <p class="text-xs text-gray-600 truncate">慢性支气管炎急性发作</p>
                </div>
                <div class="mt-2 flex items-center">
                    <span class="bg-level1/10 text-level1 text-xs px-2 py-0.5 rounded-full">一级护理</span>
                </div>
                <div class="mt-3 flex justify-end">
                    <button class="text-secondary text-sm hover:text-primary transition-colors">
                        <i class="fa fa-eye mr-1"></i>详情
                    </button>
                </div>
            </div>
            
            <!-- 10号床：三级护理 + 正常床位 -->
            <div class="bg-white rounded-lg border-2 border-level3 p-3 card-shadow bed-hover">
                <div class="flex justify-between items-start mb-2">
                    <span class="font-bold text-gray-800">10床</span>
                    <span class="bg-available/10 text-available text-xs px-2 py-1 rounded-full">
                        正常
                    </span>
                </div>
                <div class="border-t border-gray-100 pt-2">
                    <p class="font-medium text-gray-800 mb-1">刘芳</p>
                    <p class="text-xs text-gray-500 mb-1">62岁 · 女</p>
                    <p class="text-xs text-gray-600 truncate">骨质疏松症</p>
                </div>
                <div class="mt-2 flex items-center">
                    <span class="bg-level3/10 text-level3 text-xs px-2 py-0.5 rounded-full">三级护理</span>
                </div>
                <div class="mt-3 flex justify-end">
                    <button class="text-secondary text-sm hover:text-primary transition-colors">
                        <i class="fa fa-eye mr-1"></i>详情
                    </button>
                </div>
            </div>
            
            <!-- 11号床：病重 + 特级护理 + 正常床位 -->
            <div class="bg-white rounded-lg border-2 border-special p-3 card-shadow bed-hover">
                <div class="flex justify-between items-start mb-2">
                    <span class="font-bold text-gray-800">11床</span>
                    <span class="bg-available/10 text-available text-xs px-2 py-1 rounded-full">
                        正常
                    </span>
                </div>
                <div class="border-t border-gray-100 pt-2">
                    <p class="font-medium text-gray-800 mb-1">孙伟</p>
                    <p class="text-xs text-gray-500 mb-1">49岁 · 男</p>
                    <p class="text-xs text-gray-600 truncate">术后重症监护</p>
                </div>
                <div class="mt-2 flex flex-wrap gap-1">
                    <span class="bg-serious text-white text-xs px-2 py-0.5 rounded-full flex items-center">
                        <i class="fa fa-warning mr-1"></i>病重
                    </span>
                    <span class="bg-special/10 text-special text-xs px-2 py-0.5 rounded-full">特级护理</span>
                </div>
                <div class="mt-3 flex justify-end">
                    <button class="text-secondary text-sm hover:text-primary transition-colors">
                        <i class="fa fa-eye mr-1"></i>详情
                    </button>
                </div>
            </div>
            
            <!-- 12号床：病危 + 特级护理 + 正常床位 -->
            <div class="bg-white rounded-lg border-2 border-special p-3 card-shadow bed-hover pulse-animation">
                <div class="flex justify-between items-start mb-2">
                    <span class="font-bold text-gray-800">12床</span>
                    <span class="bg-available/10 text-available text-xs px-2 py-1 rounded-full">
                        正常
                    </span>
                </div>
                <div class="border-t border-gray-100 pt-2">
                    <p class="font-medium text-gray-800 mb-1">吴敏</p>
                    <p class="text-xs text-gray-500 mb-1">72岁 · 女</p>
                    <p class="text-xs text-gray-600 truncate">多器官功能衰竭</p>
                </div>
                <div class="mt-2 flex flex-wrap gap-1">
                    <span class="bg-critical text-white text-xs px-2 py-0.5 rounded-full flex items-center">
                        <i class="fa fa-exclamation-triangle mr-1"></i>病危
                    </span>
                    <span class="bg-special/10 text-special text-xs px-2 py-0.5 rounded-full">特级护理</span>
                </div>
                <div class="mt-3 flex justify-end">
                    <button class="text-secondary text-sm hover:text-primary transition-colors">
                        <i class="fa fa-eye mr-1"></i>详情
                    </button>
                </div>
            </div>
            
            <!-- 13号床：二级护理 + 正常床位 -->
            <div class="bg-white rounded-lg border-2 border-level2 p-3 card-shadow bed-hover">
                <div class="flex justify-between items-start mb-2">
                    <span class="font-bold text-gray-800">13床</span>
                    <span class="bg-available/10 text-available text-xs px-2 py-1 rounded-full">
                        正常
                    </span>
                </div>
                <div class="border-t border-gray-100 pt-2">
                    <p class="font-medium text-gray-800 mb-1">周红</p>
                    <p class="text-xs text-gray-500 mb-1">55岁 · 女</p>
                    <p class="text-xs text-gray-600 truncate">慢性胃炎</p>
                </div>
                <div class="mt-2 flex items-center">
                    <span class="bg-level2/10 text-level2 text-xs px-2 py-0.5 rounded-full">二级护理</span>
                </div>
                <div class="mt-3 flex justify-end">
                    <button class="text-secondary text-sm hover:text-primary transition-colors">
                        <i class="fa fa-eye mr-1"></i>详情
                    </button>
                </div>
            </div>
            
            <!-- 14号床：隔离状态 -->
            <div class="bg-white rounded-lg border-2 border-isolated p-3 flex flex-col items-center justify-center text-center h-full bed-hover">
                <div class="text-isolated mb-2">
                    <i class="fa fa-user-md text-2xl"></i>
                </div>
                <p class="font-bold text-gray-600 mb-1">14床</p>
                <p class="text-sm text-isolated mb-3">隔离</p>
                <button class="bg-isolated text-white text-sm px-3 py-1 rounded hover:bg-isolated/80 transition-colors w-full">
                    隔离解除
                </button>
            </div>
            
            <!-- 15号床：空床 -->
            <div class="bg-white rounded-lg border-2 border-dashed border-available p-3 flex flex-col items-center justify-center text-center h-full bed-hover">
                <div class="text-available mb-2">
                    <i class="fa fa-bed text-2xl"></i>
                </div>
                <p class="font-bold text-gray-600 mb-1">15床</p>
                <p class="text-sm text-available mb-3">空床</p>
                <button class="bg-secondary text-white text-sm px-3 py-1 rounded hover:bg-primary transition-colors w-full">
                    安排患者
                </button>
            </div>
            
            <!-- 16号床：污染状态 -->
            <div class="bg-white rounded-lg border-2 border-contaminated p-3 flex flex-col items-center justify-center text-center h-full bed-hover">
                <div class="text-contaminated mb-2">
                    <i class="fa fa-biohazard text-2xl"></i>
                </div>
                <p class="font-bold text-gray-600 mb-1">16床</p>
                <p class="text-sm text-contaminated mb-3">污染</p>
                <button class="bg-contaminated text-white text-sm px-3 py-1 rounded hover:bg-contaminated/80 transition-colors w-full">
                    清洁处理
                </button>
            </div>
        </div>
    </div>

    

    <script>
        // 床位筛选和交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 科室筛选按钮交互
            const departmentButtons = document.querySelectorAll('.flex-wrap button');
            departmentButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 移除所有按钮的活跃状态
                    departmentButtons.forEach(btn => {
                        btn.classList.remove('bg-primary', 'text-white');
                        btn.classList.add('bg-white', 'text-gray-700', 'border', 'border-gray-300', 'hover:bg-gray-50');
                    });
                    
                    // 设置当前按钮为活跃状态
                    this.classList.remove('bg-white', 'text-gray-700', 'border', 'border-gray-300', 'hover:bg-gray-50');
                    this.classList.add('bg-primary', 'text-white');
                    
                    // 筛选科室逻辑
                    const department = this.textContent.trim();
                    console.log(`筛选科室: ${department}`);
                });
            });
            
            // 床位详情按钮交互
            document.addEventListener('click', function(e) {
                if (e.target.closest('.text-secondary') && e.target.closest('.text-secondary').textContent.includes('详情')) {
                    e.preventDefault();
                    const bedCard = e.target.closest('.bed-hover');
                    if (bedCard) {
                        const bedId = bedCard.querySelector('.font-bold').textContent;
                        // 跳转到详情页
                        window.location.href = `bed_details.html?bedId=${encodeURIComponent(bedId)}`;
                    }
                }
            });
            
            // 安排患者按钮交互
            const assignButtons = document.querySelectorAll('button');
            assignButtons.forEach(button => {
                if (button.textContent.includes('安排患者')) {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        const bedId = this.closest('.bed-hover').querySelector('.font-bold').textContent;
                        alert(`为 ${bedId} 安排患者`);
                    });
                }
            });

            // 清洁处理按钮交互
            const cleanButtons = document.querySelectorAll('button');
            cleanButtons.forEach(button => {
                if (button.textContent.includes('清洁处理')) {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        const bedId = this.closest('.bed-hover').querySelector('.font-bold').textContent;
                        alert(`标记 ${bedId} 为需要清洁处理`);
                    });
                }
            });

            // 隔离解除按钮交互
            const isolationButtons = document.querySelectorAll('button');
            isolationButtons.forEach(button => {
                if (button.textContent.includes('隔离解除')) {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        const bedId = this.closest('.bed-hover').querySelector('.font-bold').textContent;
                        alert(`申请解除 ${bedId} 的隔离状态`);
                    });
                }
            });

            // 病危患者高亮提示增强
            const criticalBeds = document.querySelectorAll('.pulse-animation');
            criticalBeds.forEach(bed => {
                bed.addEventListener('mouseenter', function() {
                    this.classList.add('ring-4', 'ring-critical/30');
                });
                bed.addEventListener('mouseleave', function() {
                    this.classList.remove('ring-4', 'ring-critical/30');
                });
            });
        });
    </script>
</body>
</html>
    