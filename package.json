{"name": "admin.net", "type": "module", "version": "2.4.33", "lastBuildTime": "2025.02.14", "description": "Admin.NET 站在巨人肩膀上的 .NET 通用权限开发框架", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "scripts": {"dev": "vite", "build": "node --max-old-space-size=8192 ./node_modules/vite/bin/vite build", "lint-fix": "eslint --fix src/", "format": "prettier --write .", "build-api": "cd api_build/ && build.bat", "build-approvalFlow-api": "cd api_build/ && build.bat approvalFlow", "build-dingTalk-api": "cd api_build/ && build.bat dingTalk", "build-goView-api": "cd api_build/ && build.bat goView", "build-patient-api": "cd api_build/ && build.bat patient", "build-shared-api": "cd api_build/ && build.bat shared", "build-outpatientDoctor-api": "cd api_build/ && build.bat outpatientDoctor", "build-medicalTech-api": "cd api_build/ && build.bat medicalTech"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@logicflow/core": "^2.0.10", "@logicflow/extension": "^2.0.14", "@microsoft/signalr": "^8.0.7", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "@vue-office/pdf": "^2.0.10", "@vueuse/core": "^12.5.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "animate.css": "^4.1.1", "async-validator": "^4.2.5", "axios": "^1.7.9", "countup.js": "^2.8.0", "cropperjs": "^1.6.2", "echarts": "^5.6.0", "echarts-gl": "^2.0.9", "echarts-wordcloud": "^2.1.0", "element-plus": "^2.9.4", "ezuikit-js": "^8.1.6", "js-cookie": "^3.0.5", "js-table2excel": "^1.1.2", "json-editor-vue": "^0.17.3", "jsplumb": "^2.15.6", "lodash-es": "^4.17.21", "md-editor-v3": "^5.2.2", "mitt": "^3.0.1", "monaco-editor": "^0.52.2", "mqtt": "^5.10.3", "nprogress": "^0.2.0", "pinia": "^3.0.1", "pinyin-match": "^1.2.6", "print-js": "^1.6.0", "push.js": "^1.0.12", "qrcodejs2-fixes": "^0.0.2", "qs": "^6.14.0", "relation-graph": "^2.2.10", "screenfull": "^6.0.2", "sm-crypto-v2": "^1.9.3", "sortablejs": "^1.15.6", "splitpanes": "^3.1.8", "vcrontab-3": "^3.3.22", "vform3-builds": "^3.0.10", "vue": "^3.5.13", "vue-clipboard3": "^2.0.0", "vue-demi": "^0.14.10", "vue-draggable-plus": "^0.6.0", "vue-grid-layout": "3.0.0-beta1", "vue-i18n": "^11.1.1", "vue-json-pretty": "^2.4.0", "vue-plugin-hiprint": "0.0.59-beta2", "vue-router": "^4.5.0", "vue-signature-pad": "^3.0.2", "vue3-tree-org": "^4.2.2", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"@plugin-web-update-notification/vite": "^2.0.0", "@types/lodash-es": "^4.17.12", "@types/node": "^22.13.2", "@types/nprogress": "^0.2.3", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^8.24.0", "@typescript-eslint/parser": "^8.24.0", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vue/compiler-sfc": "^3.5.13", "code-inspector-plugin": "^0.20.0", "eslint": "^9.20.1", "eslint-plugin-vue": "^9.32.0", "globals": "^15.15.0", "less": "^4.2.2", "prettier": "^3.5.1", "rollup-plugin-visualizer": "^5.14.0", "sass": "^1.84.0", "terser": "^5.39.0", "typescript": "^5.7.3", "vite": "^6.1.0", "vite-plugin-cdn-import": "^1.0.1", "vite-plugin-compression2": "^1.3.3", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-eslint-parser": "^9.4.3"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "engines": {"node": ">=16.0.0", "npm": ">= 7.0.0"}, "keywords": ["admin.net", "vue", "vue3", "vuejs/vue-next", "element-ui", "element-plus", "vue-next-admin", "next-admin"]}